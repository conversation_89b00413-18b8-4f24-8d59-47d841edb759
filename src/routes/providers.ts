/**
 * Provider configuration routes
 * Provides information about available AI providers and their configuration
 */

import { Router } from "express";
import { AIServiceFactory } from "../services/aiServiceFactory";
import { config } from "../config";

const router = Router();

/**
 * Get provider configuration and status
 * GET /v1/providers/config
 */
router.get("/config", authenticateApi<PERSON>ey, async (req, res) => {
  try {
    console.log("[Providers] Getting provider configuration");

    // Get all provider configurations
    const providerConfigs = AIServiceFactory.getAllProvidersConfig();

    // Get provider health status
    const healthStatus = await AIServiceFactory.checkAllProvidersHealth();

    // Get provider statistics
    const stats = AIServiceFactory.getProviderStats();

    // Validate configuration
    const validationErrors = AIServiceFactory.validateConfiguration();

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      providers: {
        akash: {
          ...providerConfigs.akash,
          healthy: healthStatus.akash || false,
        },
        gemini: {
          ...providerConfigs.gemini,
          healthy: healthStatus.gemini || false,
        },
      },
      modelTiers: config.modelTiers,
      statistics: stats,
      validation: {
        isValid: validationErrors.length === 0,
        errors: validationErrors,
      },
    };

    console.log("[Providers] Provider configuration retrieved successfully");

    res.json(response);
  } catch (error) {
    console.error("[Providers] Error getting provider configuration:", error);

    res.status(500).json({
      success: false,
      error: "Failed to get provider configuration",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

/**
 * Get provider health status
 * GET /v1/providers/health
 */
router.get("/health", authenticateApiKey, async (req, res) => {
  try {
    console.log("[Providers] Checking provider health");

    const healthStatus = await AIServiceFactory.checkAllProvidersHealth();

    const allHealthy = Object.values(healthStatus).every(
      (status) => status === true
    );

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      overall: allHealthy ? "healthy" : "degraded",
      providers: healthStatus,
    };

    console.log("[Providers] Health check completed");

    res.status(allHealthy ? 200 : 503).json(response);
  } catch (error) {
    console.error("[Providers] Error checking provider health:", error);

    res.status(500).json({
      success: false,
      error: "Failed to check provider health",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

/**
 * Get available models for all providers
 * GET /v1/providers/models
 */
router.get("/models", authenticateApiKey, async (req, res) => {
  try {
    console.log("[Providers] Getting available models");

    const providers = AIServiceFactory.getAllProviders();
    const models: Record<string, string[]> = {};

    providers.forEach((provider) => {
      models[provider.name] = provider.getAvailableModels();
    });

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      models,
      currentTierConfiguration: config.modelTiers,
    };

    console.log("[Providers] Available models retrieved successfully");

    res.json(response);
  } catch (error) {
    console.error("[Providers] Error getting available models:", error);

    res.status(500).json({
      success: false,
      error: "Failed to get available models",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

export default router;
