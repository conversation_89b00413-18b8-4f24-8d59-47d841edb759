/**
 * Shared utilities for AI providers
 * This ensures consistent behavior across all AI providers (<PERSON><PERSON><PERSON>, <PERSON>, etc.)
 */

import type { AIMessage, AIAnalysisResult } from "../interfaces/aiProvider";

/**
 * Create system prompt based on filter configuration
 * This is the EXACT same prompt used across all providers to ensure consistency
 * @param filterConfig Configuration for content filtering
 * @returns System prompt
 */
export const createSystemPrompt = (
  filterConfig: Record<string, boolean>
): string => {
  // Normalize and default the filter config
  // Treat undefined as false (disallowed), but explicit true as allowed
  const normalizedConfig = {
    allowAbuse: filterConfig.allowAbuse === true,
    allowPhone: filterConfig.allowPhone === true,
    allowEmail: filterConfig.allowEmail === true,
    allowPhysicalInformation: filterConfig.allowPhysicalInformation === true,
    allowSocialInformation: filterConfig.allowSocialInformation === true,
    returnFilteredMessage: filterConfig.returnFilteredMessage === true,
  };

  // If ALL content types are allowed, return early with simple prompt
  if (
    normalizedConfig.allowAbuse &&
    normalizedConfig.allowPhone &&
    normalizedConfig.allowEmail &&
    normalizedConfig.allowPhysicalInformation &&
    normalizedConfig.allowSocialInformation
  ) {
    return `You are a content moderation AI. All content types are currently allowed by the configuration.

Your response MUST be in this EXACT JSON format:
{
  "isViolation": false,
  "flags": [],
  "reason": "All content types are allowed"${
    normalizedConfig.returnFilteredMessage ? ',\n  "filteredContent": ""' : ""
  }
}`;
  }

  // Begin standard prompt for normal cases
  let prompt = `You are a highly precise content moderation AI specializing in detecting and filtering ACTUAL sensitive information. Your CRITICAL task is to achieve near-99% accuracy in identifying ONLY REAL, COMPLETE sensitive information, even when disguised or obfuscated. Never flag vague references, incomplete data, or common phrases unless they clearly contain sensitive content. Be vigilant against bypass attempts.

STRICT DETECTION RULES:`;

  // Add rules based on configuration
  if (!normalizedConfig.allowAbuse) {
    prompt += `
- ABUSE: Flag direct insults, threats, harassment, hate speech, or explicit offensive language`;
  }

  if (!normalizedConfig.allowPhone) {
    prompt += `
- PHONE: Flag complete phone numbers in ANY format (digits, words, obfuscated)`;
  }

  if (!normalizedConfig.allowEmail) {
    prompt += `
- EMAIL: Flag complete email addresses in ANY format (standard, obfuscated, spelled out)`;
  }

  if (!normalizedConfig.allowPhysicalInformation) {
    prompt += `
- PHYSICAL: Flag complete addresses, credit card numbers, or specific location details`;
  }

  if (!normalizedConfig.allowSocialInformation) {
    prompt += `
- SOCIAL: Flag social media handles, usernames, or platform-specific identifiers`;
  }

  prompt += `

EXAMPLES NOT TO FLAG:
- "Five friends came over" → Not a phone number
- "My email is private" → No email address
- "I'm near the park" → Vague location
- "Check Instagram" → No handle

Your response MUST be in this EXACT JSON format:
{
  "isViolation": true/false,
  "flags": ["flag1", "flag2", ...],
  "reason": "Brief explanation without showing the sensitive content"`;

  if (normalizedConfig.returnFilteredMessage) {
    prompt += `,
  "filteredContent": "Original message with ALL sensitive information replaced with asterisks (*)"`;
  }

  prompt += `
}

Available flags: "abuse", "phone", "email", "address", "creditCard", "cvv", "socialMedia", "pii", "inappropriate"

CRITICAL: Only flag COMPLETE, REAL sensitive information. Ignore vague references, incomplete data, or common phrases.`;

  return prompt;
};

/**
 * Format message history for AI providers
 * @param oldMessages Previous messages
 * @param currentMessage Current message
 * @returns Formatted message history
 */
export const formatMessageHistory = (
  oldMessages: Array<any> = [],
  currentMessage: string
): AIMessage[] => {
  // If there are no previous messages or only a few, use them all
  if (oldMessages.length <= 5) {
    return [
      ...oldMessages.map((msg, index) => ({
        role: (index % 2 === 0 ? "Person1" : "Person2") as const,
        content: typeof msg === "string" ? msg : msg.text || "",
      })),
      // Add current message
      {
        role: "user" as const,
        content: currentMessage,
      },
    ];
  }

  // For longer conversations, select key messages
  const selectedIndices = new Set<number>();

  // Always include the last few messages
  for (
    let i = Math.max(0, oldMessages.length - 3);
    i < oldMessages.length;
    i++
  ) {
    selectedIndices.add(i);
  }

  // Add some earlier messages for context
  const step = Math.max(1, Math.floor(oldMessages.length / 5));
  for (let i = 0; i < oldMessages.length - 3; i += step) {
    selectedIndices.add(i);
    if (selectedIndices.size >= 8) break; // Limit to 8 historical messages
  }

  // Convert to array and sort for chronological order
  const indicesToUse = Array.from(selectedIndices).sort((a, b) => a - b);

  // Create history with selected messages
  const formattedHistory = indicesToUse.map((index) => {
    const msg = oldMessages[index];
    return {
      role: (index % 2 === 0 ? "Person1" : "Person2") as const,
      content: typeof msg === "string" ? msg : msg.text || "",
    };
  });

  // Add current message at the end
  formattedHistory.push({
    role: "user" as const,
    content: currentMessage,
  });

  // Add marker to indicate this is a summarized conversation
  if (indicesToUse.length < oldMessages.length) {
    // Insert metadata about skipped messages at the beginning
    formattedHistory.unshift({
      role: "system" as const,
      content: `Note: This is a summarized conversation history of ${
        oldMessages.length
      } total messages. ${
        formattedHistory.length - 1
      } key messages were selected for context.`,
    });
  }

  return formattedHistory;
};

/**
 * Parse AI response to extract moderation result
 * This works with responses from any AI provider
 * @param aiResponse Raw AI response
 * @returns Parsed moderation result
 */
export const parseAiResponse = (aiResponse: string): AIAnalysisResult => {
  try {
    // Try to extract JSON from the response
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);

    if (jsonMatch) {
      const jsonStr = jsonMatch[0];
      const parsed = JSON.parse(jsonStr);

      // Validate the parsed response
      if (typeof parsed.isViolation === "boolean") {
        return {
          isViolation: parsed.isViolation,
          flags: Array.isArray(parsed.flags) ? parsed.flags : [],
          reason: parsed.reason || "No reason provided",
          filteredContent: parsed.filteredContent || undefined,
        };
      }
    }
  } catch (error) {
    console.log(`[AI Parsing] JSON parsing failed:`, error);
  }

  // If no valid JSON found, use simplified extraction method
  console.log(`[AI Parsing] Using fallback parsing method`);
  const containsViolation = aiResponse.toLowerCase().includes("violation");

  // Extract flags using a simpler approach
  const potentialFlags = [
    "abuse",
    "phone",
    "email",
    "address",
    "creditCard",
    "cvv",
    "socialMedia",
    "pii",
    "inappropriate",
  ];

  const extractedFlags = potentialFlags.filter((flag) =>
    aiResponse.toLowerCase().includes(flag.toLowerCase())
  );

  return {
    isViolation: containsViolation,
    flags: extractedFlags.length > 0 ? extractedFlags : ["unknown"],
    reason: containsViolation
      ? "Content contains sensitive information"
      : "Content passed all moderation checks",
  };
};

/**
 * Validate model tier parameter
 * @param model Model tier string
 * @returns Validated model tier or 'normal' as default
 */
export const validateModelTier = (model: any): "pro" | "normal" | "fast" => {
  // If model is not provided or invalid, default to 'normal'
  if (!model || typeof model !== "string") {
    return "normal";
  }

  // Check if the model is one of the valid tiers
  const validTiers = ["pro", "normal", "fast"];
  if (validTiers.includes(model)) {
    return model as "pro" | "normal" | "fast";
  }

  // If invalid tier provided, log warning and default to 'normal'
  console.log(`[Model] Invalid model tier '${model}', defaulting to 'normal'`);
  return "normal";
};
