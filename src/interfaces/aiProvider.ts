/**
 * Common interface for AI providers in SanityAI
 * This ensures all AI providers (<PERSON><PERSON><PERSON>, <PERSON>, etc.) have consistent behavior
 */

export interface AIAnalysisResult {
  isViolation: boolean;
  flags: string[];
  reason: string;
  filteredContent?: string;
}

export interface AIProvider {
  /**
   * Provider name for identification and logging
   */
  readonly name: string;

  /**
   * Analyze text content for violations
   * @param text Text to analyze
   * @param oldMessages Previous messages for context
   * @param filterConfig Configuration for content filtering
   * @param modelName Specific model name to use
   * @returns Analysis result with flags, reasoning, and filtered content
   */
  analyzeTextContent(
    text: string,
    oldMessages: Array<any>,
    filterConfig: Record<string, boolean>,
    modelName: string
  ): Promise<AIAnalysisResult>;

  /**
   * Check if the provider is healthy and can process requests
   * @returns Promise resolving to health status
   */
  isHealthy(): Promise<boolean>;

  /**
   * Get list of available models for this provider
   * @returns Array of model names
   */
  getAvailableModels(): string[];

  /**
   * Get provider-specific configuration
   * @returns Provider configuration object
   */
  getConfig(): Record<string, any>;
}

export interface ModelTierConfig {
  provider: 'akash' | 'gemini';
  model: string;
}

export type ModelTier = 'pro' | 'normal' | 'fast';

/**
 * Common message format for AI providers
 */
export interface AIMessage {
  role: 'system' | 'user' | 'assistant' | 'Person1' | 'Person2';
  content: string;
}

/**
 * Pre-screening result interface
 */
export interface PreScreeningResult {
  needsReview: boolean;
  flags: string[];
  reason?: string;
}

/**
 * Provider factory interface
 */
export interface AIProviderFactory {
  getProvider(providerName: string): AIProvider;
  getProviderForTier(tier: ModelTier): AIProvider;
  getAllProviders(): AIProvider[];
}
