/**
 * AI Service Factory for provider-agnostic AI operations
 * Routes requests to appropriate AI providers (<PERSON><PERSON><PERSON>, Gemini, etc.)
 */

import type {
  AIProvider,
  ModelTier,
  ModelTierConfig,
} from "../interfaces/aiProvider";
import { config } from "../config";
import { GeminiProvider } from "./geminiService";
import { AkashChatProvider } from "./akashChatProvider";

/**
 * Factory class for creating and managing AI providers
 */
export class AIServiceFactory {
  private static providerInstances: Map<string, AIProvider> = new Map();

  /**
   * Get AI provider instance by name
   * @param providerName Name of the provider ('akash' or 'gemini')
   * @returns AI provider instance
   */
  static getProvider(providerName: string): AIProvider {
    // Check if we already have an instance
    if (this.providerInstances.has(providerName)) {
      return this.providerInstances.get(providerName)!;
    }

    // Create new instance based on provider name
    let provider: AIProvider;

    switch (providerName.toLowerCase()) {
      case "akash":
        provider = new AkashChatProvider();
        break;
      case "gemini":
        provider = new GeminiProvider();
        break;
      default:
        throw new Error(
          `Unknown AI provider: ${providerName}. Supported providers: akash, gemini`
        );
    }

    // Cache the instance
    this.providerInstances.set(providerName, provider);
    console.log(`[AI Factory] Created new ${providerName} provider instance`);

    return provider;
  }

  /**
   * Get AI provider for a specific model tier
   * @param tier Model tier ('pro', 'normal', 'fast')
   * @returns AI provider instance configured for the tier
   */
  static getProviderForTier(tier: ModelTier): AIProvider {
    const tierConfig = config.modelTiers[tier];

    if (!tierConfig) {
      throw new Error(
        `Invalid model tier: ${tier}. Supported tiers: pro, normal, fast`
      );
    }

    console.log(
      `[AI Factory] Getting provider for tier '${tier}': ${tierConfig.provider} with model '${tierConfig.model}'`
    );

    return this.getProvider(tierConfig.provider);
  }

  /**
   * Get model configuration for a specific tier
   * @param tier Model tier
   * @returns Model tier configuration
   */
  static getModelConfigForTier(tier: ModelTier): ModelTierConfig {
    const tierConfig = config.modelTiers[tier];

    if (!tierConfig) {
      throw new Error(
        `Invalid model tier: ${tier}. Supported tiers: pro, normal, fast`
      );
    }

    return tierConfig;
  }

  /**
   * Get all available providers
   * @returns Array of all AI provider instances
   */
  static getAllProviders(): AIProvider[] {
    const providers: AIProvider[] = [];

    try {
      providers.push(this.getProvider("akash"));
    } catch (error) {
      console.warn("[AI Factory] Akash provider not available:", error);
    }

    try {
      providers.push(this.getProvider("gemini"));
    } catch (error) {
      console.warn("[AI Factory] Gemini provider not available:", error);
    }

    return providers;
  }

  /**
   * Check health of all providers
   * @returns Promise resolving to health status of all providers
   */
  static async checkAllProvidersHealth(): Promise<Record<string, boolean>> {
    const providers = this.getAllProviders();
    const healthStatus: Record<string, boolean> = {};

    await Promise.all(
      providers.map(async (provider) => {
        try {
          healthStatus[provider.name] = await provider.isHealthy();
        } catch (error) {
          console.error(
            `[AI Factory] Health check failed for ${provider.name}:`,
            error
          );
          healthStatus[provider.name] = false;
        }
      })
    );

    return healthStatus;
  }

  /**
   * Get configuration for all providers
   * @returns Configuration object for all providers
   */
  static getAllProvidersConfig(): Record<string, any> {
    const providers = this.getAllProviders();
    const configs: Record<string, any> = {};

    providers.forEach((provider) => {
      configs[provider.name] = provider.getConfig();
    });

    return configs;
  }

  /**
   * Analyze text content using the appropriate provider for the given tier
   * @param text Text to analyze
   * @param oldMessages Previous messages for context
   * @param filterConfig Configuration for content filtering
   * @param tier Model tier to use
   * @returns Analysis result
   */
  static async analyzeTextContent(
    text: string,
    oldMessages: Array<any> = [],
    filterConfig: Record<string, boolean> = {},
    tier: ModelTier = "normal"
  ) {
    const tierConfig = this.getModelConfigForTier(tier);
    const provider = this.getProviderForTier(tier);

    console.log(
      `[AI Factory] Analyzing text with ${tierConfig.provider} provider using model '${tierConfig.model}' (tier: ${tier})`
    );

    return provider.analyzeTextContent(
      text,
      oldMessages,
      filterConfig,
      tierConfig.model
    );
  }

  /**
   * Clear all cached provider instances (useful for testing or config changes)
   */
  static clearProviderCache(): void {
    this.providerInstances.clear();
    console.log("[AI Factory] Provider cache cleared");
  }

  /**
   * Validate that all configured providers are available
   * @returns Array of validation errors (empty if all valid)
   */
  static validateConfiguration(): string[] {
    const errors: string[] = [];

    // Check each tier configuration
    Object.entries(config.modelTiers).forEach(([tier, tierConfig]) => {
      try {
        this.getProvider(tierConfig.provider);
      } catch (error) {
        errors.push(
          `Tier '${tier}' configured with invalid provider '${tierConfig.provider}': ${error}`
        );
      }
    });

    // Check API keys
    if (!config.akashChat.apiKey) {
      errors.push("Akash Chat API key is missing (AKASH_CHAT_API_KEY)");
    }

    if (!config.gemini.apiKey) {
      errors.push("Gemini API key is missing (GEMINI_API_KEY)");
    }

    return errors;
  }

  /**
   * Get provider statistics
   * @returns Statistics about provider usage
   */
  static getProviderStats(): Record<string, any> {
    const stats = {
      totalProviders: this.providerInstances.size,
      activeProviders: Array.from(this.providerInstances.keys()),
      tierConfiguration: config.modelTiers,
      lastUpdated: new Date().toISOString(),
    };

    return stats;
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use AIServiceFactory.analyzeTextContent instead
 */
export const analyzeTextContentWithFactory =
  AIServiceFactory.analyzeTextContent;

/**
 * Helper function to get provider for tier (for external use)
 */
export const getProviderForTier = AIServiceFactory.getProviderForTier;

/**
 * Helper function to get model config for tier (for external use)
 */
export const getModelConfigForTier = AIServiceFactory.getModelConfigForTier;
